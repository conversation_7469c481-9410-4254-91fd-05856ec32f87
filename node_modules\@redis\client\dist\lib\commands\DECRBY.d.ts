import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Decrements the integer value of a key by the given number
     * @param parser - The Redis command parser
     * @param key - Key to decrement
     * @param decrement - Decrement amount
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, decrement: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=DECRBY.d.ts.map