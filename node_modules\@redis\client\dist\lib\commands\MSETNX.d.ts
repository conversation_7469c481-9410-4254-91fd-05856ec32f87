import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
import { MSetArguments } from './MSET';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the MSETNX command
     *
     * @param parser - The command parser
     * @param toSet - Key-value pairs to set if none of the keys exist (array of tuples, flat array, or object)
     * @see https://redis.io/commands/msetnx/
     */
    readonly parseCommand: (this: void, parser: CommandParser, toSet: MSetArguments) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=MSETNX.d.ts.map