import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns the number of keys in the current database
     * @param parser - The Redis command parser
     */
    readonly parseCommand: (this: void, parser: CommandParser) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=DBSIZE.d.ts.map