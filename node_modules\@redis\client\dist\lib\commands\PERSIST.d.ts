import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the PERSIST command
     *
     * @param parser - The command parser
     * @param key - The key to remove the expiration from
     * @see https://redis.io/commands/persist/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=PERSIST.d.ts.map