import { CommandParser } from '../client/parser';
import { BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the MEMORY DOCTOR command
     *
     * @param parser - The command parser
     * @see https://redis.io/commands/memory-doctor/
     */
    readonly parseCommand: (this: void, parser: CommandParser) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=MEMORY_DOCTOR.d.ts.map