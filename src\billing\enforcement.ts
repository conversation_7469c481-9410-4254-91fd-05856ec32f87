import { Pool } from 'pg';
import { redisClient } from '../sessions/redis-store';

const pgPool = new Pool();

interface QuotaUsage {
  event_count: number;
  session_count: number;
  user_count: number;
  [key: string]: number; // Index signature for type safety
}

export class BillingEnforcer {
  private async getQuotaUsage(orgId: string): Promise<QuotaUsage> {
    const { rows } = await pgPool.query(
      'SELECT * FROM quota_usage WHERE organization_id = $1',
      [orgId]
    );
    return rows[0] || { event_count: 0, session_count: 0, user_count: 0 };
  }

  async checkQuota(orgId: string, resourceType: keyof QuotaUsage): Promise<boolean> {
    // Check Redis cache first
    const cached = await redisClient.get(`quota:${orgId}:${resourceType}`);
    if (cached) {
      return JSON.parse(cached);
    }

    const usage = await this.getQuotaUsage(orgId);
    const limits = await this.getOrgLimits(orgId);
    
    const limit = limits[resourceType] || 0;
    const currentUsage = usage[resourceType] || 0;
    const withinLimit = currentUsage < limit;

    // Cache result for 5 minutes
    await redisClient.setEx(
      `quota:${orgId}:${resourceType}`,
      300,
      JSON.stringify(withinLimit)
    );

    return withinLimit;
  }

  private async getOrgLimits(orgId: string): Promise<Record<string, number>> {
    const { rows } = await pgPool.query(
      'SELECT quota_limits FROM organizations WHERE id = $1',
      [orgId]
    );
    return rows[0]?.quota_limits || {};
  }

  async enforceQuota(orgId: string, resourceType: keyof QuotaUsage): Promise<void> {
    const allowed = await this.checkQuota(orgId, resourceType);
    if (!allowed) {
      throw new Error(`Quota exceeded for ${resourceType}`);
    }
  }

  async trackUsage(orgId: string, resourceType: keyof QuotaUsage, amount = 1): Promise<void> {
    await pgPool.query(
      `UPDATE organizations SET quota_limits = jsonb_set(
        quota_limits,
        '{${resourceType}}', 
        (COALESCE(quota_limits->>'${resourceType}', '0')::int + ${amount})::text::jsonb
      ) WHERE id = $1`,
      [orgId]
    );
    
    // Invalidate cached quota check
    await redisClient.del(`quota:${orgId}:${resourceType}`);
  }
}