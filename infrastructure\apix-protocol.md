# APIX WebSocket Protocol Specification

## Connection Establishment
1. Client initiates WebSocket connection to `wss://{domain}/apix/v1/ws`
2. First message must be authentication payload:
```json
{
  "token": "JWT_TOKEN",
  "channels": ["agents", "tools", "billing"]
}
```

## Message Format
```typescript
interface APIXMessage {
  event_id: string;
  event_type: 'agent' | 'tool' | 'session' | 'billing' | 'notification';
  timestamp: number;
  organization_id: string;
  payload: object;
  metadata: {
    source: 'user' | 'system';
    correlation_id?: string;
    priority: number;
  };
}
```

## Event Types
| Event Type     | Payload Structure                          | QoS |
|----------------|--------------------------------------------|-----|
| agent_executed | {session_id, input, output, cost}          | 1   |
| tool_called    | {tool_id, parameters, result, duration}    | 1   |
| session_start  | {user_id, agent_ids, initial_context}      | 0   |
| quota_exceeded | {organization_id, resource_type, limit}    | 2   |
| notification   | {channel, message, urgency, recipients}    | 1   |

## Quality of Service Levels
- 0: Fire-and-forget
- 1: At-least-once delivery
- 2: Exactly-once semantic

## Error Handling
```json
{
  "error_code": "QUOTA_EXCEEDED",
  "message": "Organization quota limit reached",
  "details": {
    "resource": "agent_executions",
    "limit": 1000,
    "used": 1000
  }
}
```

## Rate Limiting
- 1000 messages/second per organization
- Burst capacity: 5000 messages
- Global limit: 100,000 messages/second