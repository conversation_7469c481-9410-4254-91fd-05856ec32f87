declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Executes a read-only Lua script server side
     * @param parser - The Redis command parser
     * @param script - <PERSON>a script to execute
     * @param options - Script execution options including keys and arguments
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, script: import("../RESP/types").RedisArgument, options?: import("./EVAL").EvalOptions | undefined) => void;
    readonly transformReply: () => import("../RESP/types").ReplyUnion;
};
export default _default;
//# sourceMappingURL=EVAL_RO.d.ts.map