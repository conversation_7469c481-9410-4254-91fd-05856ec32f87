import { RedisSessionStore } from '../sessions/redis-store';
import { verifyToken } from '../auth/jwt';
import { AgentSession } from '../types';

export class ExecutionEngine {
  private sessionStore: RedisSessionStore;

  constructor() {
    this.sessionStore = new RedisSessionStore();
  }

  async executeAgent(sessionId: string, token: string): Promise<AgentSession> {
    if (!verifyToken(token)) {
      throw new Error('Invalid authentication token');
    }

    const session = await this.sessionStore.getSession(sessionId) || {
      id: sessionId,
      state: {},
      timestamp: Date.now()
    };

    // Update session timestamp
    session.timestamp = Date.now();
    await this.sessionStore.saveSession(session);

    return session;
  }

  async createAgentSession(initialState: Record<string, any>): Promise<AgentSession> {
    const session = {
      id: `sess_${Date.now()}`,
      state: initialState,
      timestamp: Date.now()
    };
    
    await this.sessionStore.saveSession(session);
    return session;
  }
}