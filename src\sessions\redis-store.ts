import { createClient } from 'redis';
import { Pool } from 'pg';
import { SessionData } from '../types';

const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
export const redisClient = createClient({ url: REDIS_URL });
const pgPool = new Pool();

interface SessionRecord {
  id: string;
  user_id: string;
  organization_id: string;
  token_hash: string;
  expires_at: Date;
  last_active: Date;
  metadata: Record<string, any>;
}

export class RedisSessionStore {
  constructor() {
    redisClient.connect().catch(console.error);
  }

  async getSession(sessionId: string): Promise<AgentSession | null> {
    const data = await redisClient.get(`session:${sessionId}`);
    return data ? JSON.parse(data) : null;
  }

  async saveSession(session: AgentSession): Promise<void> {
    await redisClient.set(
      `session:${session.id}`,
      JSON.stringify(session)
    );
  }

    // Fallback to PostgreSQL
    const { rows } = await pgPool.query<SessionRecord>(
      'SELECT * FROM sessions WHERE id = $1',
      [sessionId]
    );

    if (!rows.length) return null;
    
    const session = rows[0];
    const ttl = Math.floor((session.expires_at.getTime() - Date.now()) / 1000);
    
    if (ttl > 0) {
      await redisClient.setEx(
        `session:${sessionId}`,
        ttl,
        JSON.stringify(session)
      );
    }
    
    return session;
  }

  async set(sessionId: string, sessionData: SessionData) {
    const ttl = Math.floor(
      (new Date(sessionData.expires_at).getTime() - Date.now()) / 1000
    );

    await Promise.all([
      redisClient.setEx(
        `session:${sessionId}`,
        ttl,
        JSON.stringify(sessionData)
      ),
      pgPool.query(
        `INSERT INTO sessions 
         (id, user_id, organization_id, token_hash, expires_at, metadata)
         VALUES ($1, $2, $3, $4, $5, $6)
         ON CONFLICT (id) DO UPDATE SET
           expires_at = EXCLUDED.expires_at,
           metadata = EXCLUDED.metadata`,
        [
          sessionId,
          sessionData.user_id,
          sessionData.organization_id,
          sessionData.token_hash,
          sessionData.expires_at,
          sessionData.metadata
        ]
      )
    ]);
  }

  async delete(sessionId: string) {
    await Promise.all([
      redisClient.del(`session:${sessionId}`),
      pgPool.query('DELETE FROM sessions WHERE id = $1', [sessionId])
    ]);
  }

  async updateLastActive(sessionId: string) {
    const now = new Date();
    try {
      const session = await this.get(sessionId);
      const expiresAt = session?.expires_at || new Date(Date.now() + 3600000); // 1 hour default
      
      await Promise.all([
        redisClient.expire(`session:${sessionId}`,
          Math.floor((expiresAt.getTime() - Date.now()) / 1000)
        ),
        pgPool.query(
          'UPDATE sessions SET last_active = $1 WHERE id = $2',
          [now, sessionId]
        )
      ]);
    } catch (err) {
      console.error('Error updating session last active:', err);
    }
  }

  async disconnect() {
    await Promise.all([
      redisClient.quit(),
      pgPool.end()
    ]);
  }
}

export const sessionStore = new RedisSessionStore();