import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PFADD command
     *
     * @param parser - The command parser
     * @param key - The key of the HyperLogLog
     * @param element - Optional elements to add
     * @see https://redis.io/commands/pfadd/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, element?: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=PFADD.d.ts.map