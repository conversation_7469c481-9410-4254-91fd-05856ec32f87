import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LINDEX command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param index - The index of the element to retrieve
     * @see https://redis.io/commands/lindex/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, index: number) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=LINDEX.d.ts.map