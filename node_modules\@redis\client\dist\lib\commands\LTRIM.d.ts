import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the LTRIM command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param start - The starting index
     * @param stop - The ending index
     * @see https://redis.io/commands/ltrim/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, start: number, stop: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=LTRIM.d.ts.map