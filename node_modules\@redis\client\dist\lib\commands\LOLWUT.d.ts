import { CommandParser } from '../client/parser';
import { BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LOLWUT command
     *
     * @param parser - The command parser
     * @param version - Optional version parameter
     * @param optionalArguments - Additional optional numeric arguments
     * @see https://redis.io/commands/lolwut/
     */
    readonly parseCommand: (this: void, parser: CommandParser, version?: number, ...optionalArguments: Array<number>) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=LOLWUT.d.ts.map