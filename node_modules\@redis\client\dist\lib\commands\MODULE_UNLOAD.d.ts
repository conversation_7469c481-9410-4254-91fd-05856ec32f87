import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the MODULE UNLOAD command
     *
     * @param parser - The command parser
     * @param name - The name of the module to unload
     * @see https://redis.io/commands/module-unload/
     */
    readonly parseCommand: (this: void, parser: CommandParser, name: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=MODULE_UNLOAD.d.ts.map